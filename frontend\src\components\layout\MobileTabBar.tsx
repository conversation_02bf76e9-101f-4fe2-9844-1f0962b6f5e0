import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Badge } from 'antd';
import {
  HomeOutlined,
  HomeFilled,
  BookOutlined,
  BookFilled,
  ShoppingCartOutlined,
  ShoppingCartFilled,
  UserOutlined,
  UserFilled
} from '@ant-design/icons';
import styled from 'styled-components';
import { useCartStore } from '../../stores/cartStore';
import { useAuthStore } from '../../stores/authStore';

const TabBarContainer = styled.div`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  padding: 8px 0 calc(8px + env(safe-area-inset-bottom));
  display: flex;
  
  @media (min-width: 769px) {
    display: none;
  }
`;

const TabBarItem = styled.div<{ active: boolean }>`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 4px;
  cursor: pointer;
  color: ${props => props.active ? '#1677ff' : '#8c8c8c'};
  transition: color 0.3s ease;
  position: relative;
  
  .tabbar-icon {
    font-size: 20px;
    margin-bottom: 4px;
    transition: transform 0.2s ease;
  }
  
  .tabbar-label {
    font-size: 12px;
    line-height: 1;
    font-weight: ${props => props.active ? '600' : '400'};
  }
  
  &:active {
    .tabbar-icon {
      transform: scale(0.95);
    }
  }
`;

const BadgeWrapper = styled.div`
  position: relative;
  
  .ant-badge {
    .ant-badge-count {
      background: #ff4d4f;
      border: 2px solid white;
      box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
      font-size: 10px;
      min-width: 16px;
      height: 16px;
      line-height: 12px;
      padding: 0 4px;
      border-radius: 8px;
      top: -8px;
      right: -8px;
    }
  }
`;

interface TabBarItemConfig {
  key: string;
  label: string;
  icon: React.ReactNode;
  activeIcon: React.ReactNode;
  path: string;
  badge?: number;
  requireAuth?: boolean;
}

interface MobileTabBarProps {
  className?: string;
}

const MobileTabBar: React.FC<MobileTabBarProps> = ({ className }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { items } = useCartStore();
  const { isAuthenticated } = useAuthStore();

  // 计算购物车商品数量
  const cartItemCount = items.reduce((total, item) => total + item.quantity, 0);

  // 标签页配置
  const tabItems: TabBarItemConfig[] = [
    {
      key: 'home',
      label: '首页',
      icon: <HomeOutlined />,
      activeIcon: <HomeFilled />,
      path: '/'
    },
    {
      key: 'books',
      label: '图书',
      icon: <BookOutlined />,
      activeIcon: <BookFilled />,
      path: '/books'
    },
    {
      key: 'cart',
      label: '购物车',
      icon: <ShoppingCartOutlined />,
      activeIcon: <ShoppingCartFilled />,
      path: '/cart',
      badge: cartItemCount
    },
    {
      key: 'profile',
      label: '我的',
      icon: <UserOutlined />,
      activeIcon: <UserFilled />,
      path: isAuthenticated ? '/profile' : '/login'
    }
  ];

  // 获取当前激活的标签页
  const getActiveKey = () => {
    const path = location.pathname;
    
    if (path === '/') return 'home';
    if (path.startsWith('/books')) return 'books';
    if (path.startsWith('/cart')) return 'cart';
    if (path.startsWith('/profile') || path.startsWith('/login') || path.startsWith('/register')) {
      return 'profile';
    }
    
    return '';
  };

  const activeKey = getActiveKey();

  const handleTabClick = (item: TabBarItemConfig) => {
    // 如果需要认证但用户未登录，跳转到登录页
    if (item.requireAuth && !isAuthenticated) {
      navigate('/login');
      return;
    }

    navigate(item.path);
  };

  return (
    <TabBarContainer className={className}>
      {tabItems.map(item => {
        const isActive = activeKey === item.key;
        
        return (
          <TabBarItem
            key={item.key}
            active={isActive}
            onClick={() => handleTabClick(item)}
          >
            <BadgeWrapper>
              {item.badge && item.badge > 0 ? (
                <Badge count={item.badge} size="small">
                  <div className="tabbar-icon">
                    {isActive ? item.activeIcon : item.icon}
                  </div>
                </Badge>
              ) : (
                <div className="tabbar-icon">
                  {isActive ? item.activeIcon : item.icon}
                </div>
              )}
            </BadgeWrapper>
            <div className="tabbar-label">{item.label}</div>
          </TabBarItem>
        );
      })}
    </TabBarContainer>
  );
};

export default MobileTabBar;
