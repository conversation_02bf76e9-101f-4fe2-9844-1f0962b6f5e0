import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  Col, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>, 
  Card, 
  Carousel, 
  Space, 
  Statistic,
  Divider,
  Tag,
  Grid
} from 'antd';
import {
  BookOutlined,
  UserOutlined,
  ShoppingOutlined,
  TrophyOutlined,
  RightOutlined,
  FireOutlined,
  <PERSON>boltOutlined,
  CrownOutlined,
  StarOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import { Book, Category } from '../../types';
import { booksService } from '../../services/books';
import { categoriesService } from '../../services/categories';
import EnhancedBookCard from '../../components/business/EnhancedBookCard';
import LoadingState from '../../components/ui/LoadingState';

const { Title, Paragraph, Text } = Typography;
const { useBreakpoint } = Grid;

const HomeContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding-top: 72px;
`;

const HeroSection = styled.section`
  position: relative;
  height: 600px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/books-pattern.png') repeat;
    opacity: 0.1;
  }
  
  .hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    max-width: 800px;
    padding: 0 24px;
    
    .hero-title {
      font-size: 3.5rem;
      font-weight: 800;
      margin-bottom: 24px;
      text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      
      @media (max-width: 768px) {
        font-size: 2.5rem;
      }
    }
    
    .hero-subtitle {
      font-size: 1.5rem;
      margin-bottom: 40px;
      opacity: 0.9;
      
      @media (max-width: 768px) {
        font-size: 1.2rem;
      }
    }
    
    .hero-actions {
      display: flex;
      gap: 16px;
      justify-content: center;
      flex-wrap: wrap;
      
      .hero-btn {
        height: 50px;
        padding: 0 32px;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        border: none;
        transition: all 0.3s ease;
        
        &.primary {
          background: linear-gradient(135deg, #ff6b6b, #ee5a24);
          
          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
          }
        }
        
        &.secondary {
          background: rgba(255, 255, 255, 0.2);
          backdrop-filter: blur(10px);
          color: white;
          border: 2px solid rgba(255, 255, 255, 0.3);
          
          &:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-3px);
          }
        }
      }
    }
  }
  
  .hero-decoration {
    position: absolute;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    
    &.decoration-1 {
      top: 10%;
      left: 10%;
      animation: float 6s ease-in-out infinite;
    }
    
    &.decoration-2 {
      bottom: 10%;
      right: 10%;
      animation: float 6s ease-in-out infinite reverse;
    }
  }
  
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }
`;

const StatsSection = styled.section`
  padding: 80px 24px;
  background: white;
  
  .stats-container {
    max-width: 1200px;
    margin: 0 auto;
    
    .stats-grid {
      .stat-card {
        text-align: center;
        padding: 40px 20px;
        border-radius: 16px;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border: 1px solid #dee2e6;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-8px);
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .stat-icon {
          font-size: 48px;
          margin-bottom: 16px;
          background: linear-gradient(135deg, #667eea, #764ba2);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
        
        .ant-statistic-content {
          font-size: 2.5rem;
          font-weight: 800;
          color: #2c3e50;
        }
        
        .stat-label {
          font-size: 16px;
          color: #6c757d;
          margin-top: 8px;
        }
      }
    }
  }
`;

const FeaturedSection = styled.section`
  padding: 80px 24px;
  background: #f8f9fa;
  
  .section-header {
    text-align: center;
    margin-bottom: 60px;
    
    .section-title {
      font-size: 2.5rem;
      font-weight: 800;
      color: #2c3e50;
      margin-bottom: 16px;
    }
    
    .section-subtitle {
      font-size: 1.2rem;
      color: #6c757d;
      max-width: 600px;
      margin: 0 auto;
    }
  }
  
  .featured-tabs {
    max-width: 1400px;
    margin: 0 auto;
    
    .tab-buttons {
      display: flex;
      justify-content: center;
      gap: 16px;
      margin-bottom: 40px;
      flex-wrap: wrap;
      
      .tab-btn {
        padding: 12px 24px;
        border-radius: 25px;
        border: 2px solid #dee2e6;
        background: white;
        color: #6c757d;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
        
        &:hover {
          border-color: #667eea;
          color: #667eea;
        }
        
        &.active {
          background: linear-gradient(135deg, #667eea, #764ba2);
          border-color: transparent;
          color: white;
          box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .tab-icon {
          margin-right: 8px;
        }
      }
    }
  }
`;

const CategoriesSection = styled.section`
  padding: 80px 24px;
  background: white;
  
  .categories-grid {
    max-width: 1200px;
    margin: 0 auto;
    
    .category-card {
      padding: 30px;
      border-radius: 16px;
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      border: 1px solid #dee2e6;
      text-align: center;
      transition: all 0.3s ease;
      cursor: pointer;
      
      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        
        .category-icon {
          color: white;
        }
      }
      
      .category-icon {
        font-size: 48px;
        margin-bottom: 16px;
        color: #667eea;
        transition: color 0.3s ease;
      }
      
      .category-name {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 8px;
      }
      
      .category-count {
        font-size: 14px;
        opacity: 0.8;
      }
    }
  }
`;

interface EnhancedHomeProps {}

const EnhancedHome: React.FC<EnhancedHomeProps> = () => {
  const navigate = useNavigate();
  const screens = useBreakpoint();
  
  const [loading, setLoading] = useState(true);
  const [featuredBooks, setFeaturedBooks] = useState<Book[]>([]);
  const [hotBooks, setHotBooks] = useState<Book[]>([]);
  const [newBooks, setNewBooks] = useState<Book[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [activeTab, setActiveTab] = useState<'featured' | 'hot' | 'new'>('featured');

  // 模拟统计数据
  const stats = [
    { icon: <BookOutlined />, value: 10000, label: '图书总数' },
    { icon: <UserOutlined />, value: 5000, label: '注册用户' },
    { icon: <ShoppingOutlined />, value: 15000, label: '成功交易' },
    { icon: <TrophyOutlined />, value: 98, label: '满意度' }
  ];

  // 标签页配置
  const tabs = [
    { key: 'featured', label: '精选推荐', icon: <StarOutlined /> },
    { key: 'hot', label: '热销图书', icon: <FireOutlined /> },
    { key: 'new', label: '新品上架', icon: <ThunderboltOutlined /> }
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // 并行加载数据
      const [booksRes, categoriesRes] = await Promise.all([
        booksService.getBooks({ limit: 12, sort: 'views_DESC' }),
        categoriesService.getCategories()
      ]);

      if (booksRes.success && booksRes.data) {
        const books = booksRes.data.books || [];
        setFeaturedBooks(books.slice(0, 8));
        setHotBooks(books.filter(book => book.sales_count > 10).slice(0, 8));
        setNewBooks(books.filter(book => 
          new Date(book.created_at).getTime() > Date.now() - 30 * 24 * 60 * 60 * 1000
        ).slice(0, 8));
      }

      if (categoriesRes.success && categoriesRes.data) {
        setCategories(categoriesRes.data.slice(0, 8));
      }
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCurrentBooks = () => {
    switch (activeTab) {
      case 'hot':
        return hotBooks;
      case 'new':
        return newBooks;
      default:
        return featuredBooks;
    }
  };

  if (loading) {
    return <LoadingState loading={true} minHeight="100vh" />;
  }

  return (
    <HomeContainer>
      {/* 英雄区域 */}
      <HeroSection>
        <div className="hero-decoration decoration-1"></div>
        <div className="hero-decoration decoration-2"></div>
        
        <div className="hero-content">
          <h1 className="hero-title">发现知识的宝藏</h1>
          <p className="hero-subtitle">
            在这里，每一本书都是一次新的冒险，每一次阅读都是心灵的成长
          </p>
          <div className="hero-actions">
            <Button 
              className="hero-btn primary"
              size="large"
              onClick={() => navigate('/books')}
            >
              开始探索
            </Button>
            <Button 
              className="hero-btn secondary"
              size="large"
              onClick={() => navigate('/register')}
            >
              加入我们
            </Button>
          </div>
        </div>
      </HeroSection>

      {/* 统计数据 */}
      <StatsSection>
        <div className="stats-container">
          <Row gutter={[32, 32]} className="stats-grid">
            {stats.map((stat, index) => (
              <Col xs={12} sm={6} key={index}>
                <Card className="stat-card" bordered={false}>
                  <div className="stat-icon">{stat.icon}</div>
                  <Statistic 
                    value={stat.value} 
                    suffix={stat.label === '满意度' ? '%' : '+'} 
                  />
                  <div className="stat-label">{stat.label}</div>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </StatsSection>

      {/* 精选图书 */}
      <FeaturedSection>
        <div className="section-header">
          <h2 className="section-title">精选图书</h2>
          <p className="section-subtitle">
            为您精心挑选的优质图书，涵盖各个领域的知识精华
          </p>
        </div>

        <div className="featured-tabs">
          <div className="tab-buttons">
            {tabs.map(tab => (
              <div
                key={tab.key}
                className={`tab-btn ${activeTab === tab.key ? 'active' : ''}`}
                onClick={() => setActiveTab(tab.key as any)}
              >
                <span className="tab-icon">{tab.icon}</span>
                {tab.label}
              </div>
            ))}
          </div>

          <Row gutter={[24, 24]}>
            {getCurrentBooks().map(book => (
              <Col xs={24} sm={12} md={8} lg={6} key={book.id}>
                <EnhancedBookCard book={book} />
              </Col>
            ))}
          </Row>

          <div style={{ textAlign: 'center', marginTop: 40 }}>
            <Button 
              type="primary" 
              size="large"
              icon={<RightOutlined />}
              onClick={() => navigate('/books')}
            >
              查看更多图书
            </Button>
          </div>
        </div>
      </FeaturedSection>

      {/* 图书分类 */}
      <CategoriesSection>
        <div className="section-header">
          <h2 className="section-title">图书分类</h2>
          <p className="section-subtitle">
            按分类浏览，快速找到您感兴趣的图书
          </p>
        </div>

        <div className="categories-grid">
          <Row gutter={[24, 24]}>
            {categories.map(category => (
              <Col xs={12} sm={8} md={6} lg={3} key={category.id}>
                <div 
                  className="category-card"
                  onClick={() => navigate(`/books?category=${category.id}`)}
                >
                  <div className="category-icon">
                    <BookOutlined />
                  </div>
                  <div className="category-name">{category.name}</div>
                  <div className="category-count">
                    {Math.floor(Math.random() * 100) + 50} 本图书
                  </div>
                </div>
              </Col>
            ))}
          </Row>
        </div>
      </CategoriesSection>
    </HomeContainer>
  );
};

export default EnhancedHome;
